{"name": "packaging", "version": "1.0.0", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"express": "^5.1.0"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.1.0", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}