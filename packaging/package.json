{"name": "packaging", "version": "1.0.0", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/mongoose": "^5.11.96", "@types/node": "^24.1.0", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}