import mongoose, { Document, Schema } from 'mongoose';

export interface IPackage {
  name: string;
  version: string;
  description: string;
  author: string;
  dependencies?: string[];
  devDependencies?: string[];
  keywords?: string[];
  license?: string;
  repository?: string;
  homepage?: string;
  bugs?: string;
  downloads?: number;
  isPublic?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface IPackageDocument extends IPackage, Document {}

const PackageSchema = new Schema<IPackageDocument>(
  {
    name: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      minlength: 1,
      maxlength: 100,
    },
    version: {
      type: String,
      required: true,
      trim: true,
      match: /^\d+\.\d+\.\d+$/,
    },
    description: {
      type: String,
      required: true,
      trim: true,
      maxlength: 500,
    },
    author: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100,
    },
    dependencies: [{
      type: String,
      trim: true,
    }],
    devDependencies: [{
      type: String,
      trim: true,
    }],
    keywords: [{
      type: String,
      trim: true,
      maxlength: 50,
    }],
    license: {
      type: String,
      trim: true,
      maxlength: 50,
    },
    repository: {
      type: String,
      trim: true,
      maxlength: 200,
    },
    homepage: {
      type: String,
      trim: true,
      maxlength: 200,
    },
    bugs: {
      type: String,
      trim: true,
      maxlength: 200,
    },
    downloads: {
      type: Number,
      default: 0,
      min: 0,
    },
    isPublic: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Indexes
PackageSchema.index({ name: 1 });
PackageSchema.index({ author: 1 });
PackageSchema.index({ keywords: 1 });
PackageSchema.index({ createdAt: -1 });

export const Package = mongoose.model<IPackageDocument>('Package', PackageSchema);
