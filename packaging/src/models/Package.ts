import mongoose, { Document, Schema } from 'mongoose';

export interface Variations {
  title: string;
  optionType: "images" | "default" | "dropdown";
  options: string[];
}

export interface Quantity {
  size: string;
  price: number;
}

export interface IPackage {
  title: string;
  shortDescription: string;
  description: string;
  variations: Variations[];
  quantity: Quantity[];
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IPackageDocument extends IPackage, Document {}

const VariationsSchema = new Schema<Variations>({
  title: {
    type: String,
    required: [true, 'Variation title is required'],
    trim: true,
  },
  optionType: {
    type: String,
    required: [true, 'Option type is required'],
    enum: ['images', 'default', 'dropdown'],
  },
  options: [{
    type: String,
    required: true,
    trim: true,
  }],
}, { _id: false });

const QuantitySchema = new Schema<Quantity>({
  size: {
    type: String,
    required: [true, 'Size is required'],
    trim: true,
  },
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price cannot be negative'],
  },
}, { _id: false });

const PackageSchema = new Schema<IPackageDocument>(
  {
    title: {
      type: String,
      required: [true, 'Package title is required'],
      trim: true,
      maxlength: [200, 'Title cannot exceed 200 characters'],
    },
    shortDescription: {
      type: String,
      required: [true, 'Short description is required'],
      trim: true,
      maxlength: [300, 'Short description cannot exceed 300 characters'],
    },
    description: {
      type: String,
      required: [true, 'Description is required'],
      trim: true,
      maxlength: [2000, 'Description cannot exceed 2000 characters'],
    },
    variations: {
      type: [VariationsSchema],
      required: [true, 'At least one variation is required'],
      validate: {
        validator: function(v: Variations[]) {
          return v && v.length > 0;
        },
        message: 'At least one variation is required',
      },
    },
    quantity: {
      type: [QuantitySchema],
      required: [true, 'At least one quantity option is required'],
      validate: {
        validator: function(v: Quantity[]) {
          return v && v.length > 0;
        },
        message: 'At least one quantity option is required',
      },
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Indexes for better query performance
PackageSchema.index({ title: 1 });
PackageSchema.index({ createdAt: -1 });

// Text index for search functionality
PackageSchema.index({
  title: 'text',
  shortDescription: 'text',
  description: 'text'
});

export const Package = mongoose.model<IPackageDocument>('Package', PackageSchema);
