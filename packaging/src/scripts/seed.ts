import { database } from '../config/database';
import { User } from '../models/User';
import { Package } from '../models/Package';
import { logger } from '../utils/logger';

const seedData = async () => {
  try {
    // Connect to database
    await database.connect();
    
    // Clear existing data
    await User.deleteMany({});
    await Package.deleteMany({});
    
    // Create admin user
    const adminUser = new User({
      email: '<EMAIL>',
      username: 'admin',
      password: 'admin123',
      role: 'admin',
      firstName: 'Admin',
      lastName: 'User',
      isActive: true,
    });
    await adminUser.save();
    logger.info('Admin user created');
    
    // Create regular user
    const regularUser = new User({
      email: '<EMAIL>',
      username: 'testuser',
      password: 'user123',
      role: 'user',
      firstName: 'Test',
      lastName: 'User',
      isActive: true,
    });
    await regularUser.save();
    logger.info('Regular user created');
    
    // Create sample packages
    const packages = [
      {
        name: 'express-utils',
        version: '1.0.0',
        description: 'Utility functions for Express.js applications',
        author: 'admin',
        keywords: ['express', 'utilities', 'middleware'],
        license: 'MIT',
        dependencies: ['express'],
        downloads: 150,
        isPublic: true,
      },
      {
        name: 'mongodb-helper',
        version: '2.1.0',
        description: 'Helper functions for MongoDB operations',
        author: 'testuser',
        keywords: ['mongodb', 'database', 'helper'],
        license: 'MIT',
        dependencies: ['mongoose'],
        downloads: 89,
        isPublic: true,
      },
      {
        name: 'auth-middleware',
        version: '1.5.2',
        description: 'Authentication middleware for Node.js applications',
        author: 'admin',
        keywords: ['auth', 'middleware', 'jwt'],
        license: 'MIT',
        dependencies: ['jsonwebtoken', 'bcryptjs'],
        downloads: 234,
        isPublic: true,
      },
    ];
    
    for (const packageData of packages) {
      const package_ = new Package(packageData);
      await package_.save();
      logger.info(`Package created: ${packageData.name}`);
    }
    
    logger.info('Database seeded successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error seeding database:', error);
    process.exit(1);
  }
};

seedData();
