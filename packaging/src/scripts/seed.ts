import { database } from '../config/database';
import { User } from '../models/User';
import { Package } from '../models/Package';
import { logger } from '../utils/logger';

const seedData = async () => {
  try {
    // Connect to database
    await database.connect();
    
    // Clear existing data
    await User.deleteMany({});
    await Package.deleteMany({});
    
    // Create admin user
    const adminUser = new User({
      email: '<EMAIL>',
      username: 'admin',
      password: 'admin123',
      role: 'admin',
      firstName: 'Admin',
      lastName: 'User',
      isActive: true,
    });
    await adminUser.save();
    logger.info('Admin user created');
    
    // Create regular user
    const regularUser = new User({
      email: '<EMAIL>',
      username: 'testuser',
      password: 'user123',
      role: 'user',
      firstName: 'Test',
      lastName: 'User',
      isActive: true,
    });
    await regularUser.save();
    logger.info('Regular user created');
    
    // Create sample packages
    const packages = [
      {
        title: 'Premium Gift Box',
        shortDescription: 'Elegant gift packaging for special occasions',
        description: 'A beautifully crafted premium gift box perfect for birthdays, anniversaries, and special celebrations. Made with high-quality materials and available in multiple sizes.',
        variations: [
          {
            title: 'Color',
            optionType: 'default' as const,
            options: ['Red', 'Blue', 'Gold', 'Silver']
          },
          {
            title: 'Style',
            optionType: 'dropdown' as const,
            options: ['Classic', 'Modern', 'Vintage']
          }
        ],
        quantity: [
          { size: 'Small', price: 15.99 },
          { size: 'Medium', price: 25.99 },
          { size: 'Large', price: 35.99 }
        ]
      },
      {
        title: 'Eco-Friendly Packaging',
        shortDescription: 'Sustainable packaging solutions for environmentally conscious customers',
        description: 'Made from 100% recycled materials, this eco-friendly packaging option helps reduce environmental impact while maintaining product protection and aesthetic appeal.',
        variations: [
          {
            title: 'Material',
            optionType: 'default' as const,
            options: ['Recycled Cardboard', 'Bamboo Fiber', 'Biodegradable Plastic']
          }
        ],
        quantity: [
          { size: 'Standard', price: 12.99 },
          { size: 'Large', price: 18.99 }
        ]
      },
      {
        title: 'Custom Branded Box',
        shortDescription: 'Personalized packaging with your brand logo and colors',
        description: 'Create a lasting impression with custom branded packaging. Perfect for businesses looking to enhance their brand presence and customer unboxing experience.',
        variations: [
          {
            title: 'Print Quality',
            optionType: 'dropdown' as const,
            options: ['Standard Print', 'Premium Print', 'Luxury Embossed']
          },
          {
            title: 'Box Type',
            optionType: 'images' as const,
            options: ['Magnetic Closure', 'Tuck-in Flap', 'Ribbon Tie']
          }
        ],
        quantity: [
          { size: 'Small (50 units)', price: 89.99 },
          { size: 'Medium (100 units)', price: 159.99 },
          { size: 'Large (250 units)', price: 349.99 }
        ]
      }
    ];
    
    for (const packageData of packages) {
      const package_ = new Package(packageData);
      await package_.save();
      logger.info(`Package created: ${packageData.title}`);
    }
    
    logger.info('Database seeded successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error seeding database:', error);
    process.exit(1);
  }
};

seedData();
