import dotenv from 'dotenv';

dotenv.config();

export const config = {
  port: process.env.PORT || 8000,
  nodeEnv: process.env.NODE_ENV || 'development',

  mongodb: {
    uri: process.env.MONGO_URI || 'mongodb://localhost:27017/packaging_db',
    options: {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    },
  },

  jwt: {
    secret: process.env.JWT_SECRET || 'coconut-secret',
    adminSecret: process.env.ADMIN_JWT_SECRET || 'coconut-secret-admin',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },

  email: {
    user: process.env.EMAIL_USER || '',
    pass: process.env.EMAIL_PASS || '',
  },

  redis: {
    url: process.env.REDIS_URL || '',
  },

  twilio: {
    authToken: process.env.TWILIO_AUTH_TOKEN || '',
    phoneNumber: process.env.TWILIO_PHONE_NUMBER || '',
    sid: process.env.TWILIO_SID || '',
  },

  aws: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
    region: process.env.AWS_REGION || 'us-east-1',
    s3BucketName: process.env.AWS_S3_BUCKET_NAME || '',
  },

  seerbit: {
    apiUrl: process.env.SEERBIT_API_URL || '',
    publicKey: process.env.SEERBIT_PUBLIC_KEY || '',
    secretKey: process.env.SEERBIT_SECRET_KEY || '',
    merchantId: process.env.SEERBIT_MERCHANT_ID || '',
  },

  flutterwave: {
    baseUrl: process.env.FLUTTERWAVE_BASE_URL || '',
    secretKey: process.env.FLUTTERWAVE_SECRET_KEY || '',
    secretHash: process.env.FLW_SECRET_HASH || '',
  },

  wallets: {
    banking: process.env.BANKING_WALLET_ID || '',
    shipping: process.env.SHIPPING_WALLET_ID || '',
    filling: process.env.FILLING_WALLET_ID || '',
    packaging: process.env.PACKAGING_WALLET_ID || '',
    transfer: process.env.TRANSFER_WALLET_ID || '',
  },

  bani: {
    baseUrl: process.env.BANI_BASE_URL || '',
    token: process.env.BANI_TOKEN || '',
    sharedKey: process.env.BANI_SHARED_KEY || '',
    moniSignature: process.env.BANI_MONI_SIGNATURE || '',
  },

  qc: {
    baseUrl: process.env.QC_BASE_URL || '',
    token: process.env.QC_TOKEN || '',
    clientId: process.env.QC_CLIENT_ID || '',
    bookingPath: process.env.QC_BOOKING_PATH || '',
  },

  firebase: {
    projectId: process.env.FIREBASE_PROJECT_ID || '',
    bucketName: process.env.FIREBASE_BUCKET_NAME || '',
  },

  zoho: {
    key: process.env.ZOHO_KEY || '',
    gKey: process.env.G_ZOHO_KEY || '',
    url: process.env.ZOHO_URL || '',
  },

  urls: {
    api: process.env.API_URL || '',
    gate: process.env.GATE_URL || '',
  },

  cors: {
    origin: process.env.CORS_ORIGIN || '*',
  },
};
