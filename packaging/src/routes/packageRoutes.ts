import { Router } from 'express';
import { PackageController } from '../controllers/PackageController';
import { authenticate } from '../middleware/auth';

const router = Router();
const packageController = new PackageController();

router.get('/', packageController.getAllPackages);
router.get('/:id', packageController.getPackageById);
router.post('/', authenticate, packageController.createPackage);
router.put('/:id', authenticate, packageController.updatePackage);
router.delete('/:id', authenticate, packageController.deletePackage);

export default router;
