import { Router } from 'express';
import { PackageController } from '../controllers/PackageController';
import { authenticate, authorize } from '../middleware/auth';

const router = Router();
const packageController = new PackageController();

// Public routes
router.get('/', packageController.getAllPackages);
router.get('/search', packageController.searchPackages);
router.get('/:id', packageController.getPackageById);
router.get('/:id/download', packageController.downloadPackage);

// Protected routes
router.post('/', authenticate, packageController.createPackage);
router.put('/:id', authenticate, packageController.updatePackage);
router.delete('/:id', authenticate, authorize(['admin']), packageController.deletePackage);

export default router;
