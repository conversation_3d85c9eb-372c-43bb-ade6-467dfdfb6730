import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';

export const validateRequest = (schema: any) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req.body);
    
    if (error) {
      const validationError: AppError = new Error(error.details[0].message);
      validationError.statusCode = 400;
      return next(validationError);
    }
    
    next();
  };
};
