import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';
import { UserService } from '../services/UserService';
import { IUserDocument } from '../models/User';

export interface AuthenticatedRequest extends Request {
  user?: IUserDocument;
}

const userService = new UserService();

export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const token = req.header('Authorization')?.replace('Bearer ', '');

  if (!token) {
    const error: AppError = new Error('Access denied. No token provided.');
    error.statusCode = 401;
    return next(error);
  }

  try {
    // Verify JWT token
    const decoded = await userService.verifyToken(token);
    if (!decoded) {
      const error: AppError = new Error('Invalid token.');
      error.statusCode = 401;
      return next(error);
    }

    // Get user from database
    const user = await userService.getUserById(decoded.userId);
    if (!user || !user.isActive) {
      const error: AppError = new Error('User not found or inactive.');
      error.statusCode = 401;
      return next(error);
    }

    req.user = user;
    next();
  } catch (error) {
    const authError: AppError = new Error('Invalid token.');
    authError.statusCode = 401;
    next(authError);
  }
};

export const authorize = (roles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      const error: AppError = new Error('Access denied. User not authenticated.');
      error.statusCode = 401;
      return next(error);
    }

    if (!roles.includes(req.user.role)) {
      const error: AppError = new Error('Access denied. Insufficient permissions.');
      error.statusCode = 403;
      return next(error);
    }

    next();
  };
};
