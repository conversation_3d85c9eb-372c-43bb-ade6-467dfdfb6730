import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: string;
  };
}

export const authenticate = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  const token = req.header('Authorization')?.replace('Bearer ', '');

  if (!token) {
    const error: AppError = new Error('Access denied. No token provided.');
    error.statusCode = 401;
    return next(error);
  }

  try {
    // TODO: Implement JWT verification
    // const decoded = jwt.verify(token, config.jwt.secret);
    // req.user = decoded;
    next();
  } catch (error) {
    const authError: AppError = new Error('Invalid token.');
    authError.statusCode = 401;
    next(authError);
  }
};
