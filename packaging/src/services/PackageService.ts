import { Package, IPackage, IPackageDocument } from '../models/Package';
import { logger } from '../utils/logger';

export class PackageService {
  async getAllPackages(page: number = 1, limit: number = 10): Promise<{ packages: IPackageDocument[]; total: number }> {
    try {
      const skip = (page - 1) * limit;
      const packages = await Package.find({})
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec();

      const total = await Package.countDocuments({});

      return { packages, total };
    } catch (error) {
      logger.error('Error getting all packages:', error);
      throw new Error('Failed to retrieve packages');
    }
  }

  async getPackageById(id: string): Promise<IPackageDocument | null> {
    try {
      const package_ = await Package.findById(id).exec();
      return package_;
    } catch (error) {
      logger.error('Error getting package by ID:', { error, id });
      throw new Error('Failed to retrieve package');
    }
  }

  async getPackageByTitle(title: string): Promise<IPackageDocument | null> {
    try {
      const package_ = await Package.findOne({ title }).exec();
      return package_;
    } catch (error) {
      logger.error('Error getting package by title:', { error, title });
      throw new Error('Failed to retrieve package');
    }
  }

  async createPackage(packageData: Partial<IPackage>): Promise<IPackageDocument> {
    try {
      // Check if package with same title already exists
      const existingPackage = await Package.findOne({ title: packageData.title });
      if (existingPackage) {
        throw new Error('Package with this title already exists');
      }

      const newPackage = new Package(packageData);
      const savedPackage = await newPackage.save();

      logger.info('Package created successfully:', { id: savedPackage._id, title: savedPackage.title });
      return savedPackage;
    } catch (error) {
      logger.error('Error creating package:', { error, data: packageData });
      throw error;
    }
  }

  async updatePackage(id: string, updateData: Partial<IPackage>): Promise<IPackageDocument | null> {
    try {
      const updatedPackage = await Package.findByIdAndUpdate(
        id,
        { ...updateData, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).exec();

      if (updatedPackage) {
        logger.info('Package updated successfully:', { id, title: updatedPackage.title });
      }

      return updatedPackage;
    } catch (error) {
      logger.error('Error updating package:', { error, id, data: updateData });
      throw new Error('Failed to update package');
    }
  }

  async deletePackage(id: string): Promise<boolean> {
    try {
      const deletedPackage = await Package.findByIdAndDelete(id).exec();

      if (deletedPackage) {
        logger.info('Package deleted successfully:', { id, title: deletedPackage.title });
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Error deleting package:', { error, id });
      throw new Error('Failed to delete package');
    }
  }

  async searchPackages(query: string, page: number = 1, limit: number = 10): Promise<{ packages: IPackageDocument[]; total: number }> {
    try {
      const skip = (page - 1) * limit;
      const searchRegex = new RegExp(query, 'i');

      const packages = await Package.find({
        $or: [
          { title: searchRegex },
          { shortDescription: searchRegex },
          { description: searchRegex }
        ]
      })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec();

      const total = await Package.countDocuments({
        $or: [
          { title: searchRegex },
          { shortDescription: searchRegex },
          { description: searchRegex }
        ]
      });

      return { packages, total };
    } catch (error) {
      logger.error('Error searching packages:', { error, query });
      throw new Error('Failed to search packages');
    }
  }
}
