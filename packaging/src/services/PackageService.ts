import { Package, IPackage, IPackageDocument } from '../models/Package';
import { logger } from '../utils/logger';

export class PackageService {
  async getAllPackages(page: number = 1, limit: number = 10): Promise<{ packages: IPackageDocument[]; total: number }> {
    try {
      const skip = (page - 1) * limit;
      const packages = await Package.find({ isPublic: true })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec();

      const total = await Package.countDocuments({ isPublic: true });

      return { packages, total };
    } catch (error) {
      logger.error('Error getting all packages:', error);
      throw new Error('Failed to retrieve packages');
    }
  }

  async getPackageById(id: string): Promise<IPackageDocument | null> {
    try {
      const package_ = await Package.findById(id).exec();
      return package_;
    } catch (error) {
      logger.error('Error getting package by ID:', { error, id });
      throw new Error('Failed to retrieve package');
    }
  }

  async getPackageByName(name: string): Promise<IPackageDocument | null> {
    try {
      const package_ = await Package.findOne({ name }).exec();
      return package_;
    } catch (error) {
      logger.error('Error getting package by name:', { error, name });
      throw new Error('Failed to retrieve package');
    }
  }

  async createPackage(packageData: Partial<IPackage>): Promise<IPackageDocument> {
    try {
      // Check if package with same name already exists
      const existingPackage = await Package.findOne({ name: packageData.name });
      if (existingPackage) {
        throw new Error('Package with this name already exists');
      }

      const newPackage = new Package(packageData);
      const savedPackage = await newPackage.save();

      logger.info('Package created successfully:', { id: savedPackage._id, name: savedPackage.name });
      return savedPackage;
    } catch (error) {
      logger.error('Error creating package:', { error, data: packageData });
      throw error;
    }
  }

  async updatePackage(id: string, updateData: Partial<IPackage>): Promise<IPackageDocument | null> {
    try {
      const updatedPackage = await Package.findByIdAndUpdate(
        id,
        { ...updateData, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).exec();

      if (updatedPackage) {
        logger.info('Package updated successfully:', { id, name: updatedPackage.name });
      }

      return updatedPackage;
    } catch (error) {
      logger.error('Error updating package:', { error, id, data: updateData });
      throw new Error('Failed to update package');
    }
  }

  async deletePackage(id: string): Promise<boolean> {
    try {
      const deletedPackage = await Package.findByIdAndDelete(id).exec();

      if (deletedPackage) {
        logger.info('Package deleted successfully:', { id, name: deletedPackage.name });
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Error deleting package:', { error, id });
      throw new Error('Failed to delete package');
    }
  }

  async searchPackages(query: string, page: number = 1, limit: number = 10): Promise<{ packages: IPackageDocument[]; total: number }> {
    try {
      const skip = (page - 1) * limit;
      const searchRegex = new RegExp(query, 'i');

      const packages = await Package.find({
        isPublic: true,
        $or: [
          { name: searchRegex },
          { description: searchRegex },
          { keywords: { $in: [searchRegex] } }
        ]
      })
        .sort({ downloads: -1, createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec();

      const total = await Package.countDocuments({
        isPublic: true,
        $or: [
          { name: searchRegex },
          { description: searchRegex },
          { keywords: { $in: [searchRegex] } }
        ]
      });

      return { packages, total };
    } catch (error) {
      logger.error('Error searching packages:', { error, query });
      throw new Error('Failed to search packages');
    }
  }

  async incrementDownloads(id: string): Promise<void> {
    try {
      await Package.findByIdAndUpdate(id, { $inc: { downloads: 1 } }).exec();
    } catch (error) {
      logger.error('Error incrementing downloads:', { error, id });
    }
  }
}
