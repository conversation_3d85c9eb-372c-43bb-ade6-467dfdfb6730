import jwt from 'jsonwebtoken';
import { User, IUser, IUserDocument } from '../models/User';
import { config } from '../config/config';
import { logger } from '../utils/logger';

export class UserService {
  private generateToken(userId: string): string {
    return jwt.sign({ userId }, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
    });
  }

  async register(userData: Partial<IUser>): Promise<{ user: IUserDocument; token: string }> {
    try {
      // Check if user already exists
      const existingUser = await User.findOne({
        $or: [{ email: userData.email }, { username: userData.username }]
      });

      if (existingUser) {
        throw new Error('User with this email or username already exists');
      }

      // Create new user
      const newUser = new User(userData);
      const savedUser = await newUser.save();

      // Generate JWT token
      const token = this.generateToken(savedUser._id.toString());

      logger.info('User registered successfully:', { id: savedUser._id, email: savedUser.email });
      return { user: savedUser, token };
    } catch (error) {
      logger.error('Error registering user:', { error, email: userData.email });
      throw error;
    }
  }

  async login(email: string, password: string): Promise<{ user: IUserDocument; token: string }> {
    try {
      // Find user by email
      const user = await User.findOne({ email }).select('+password');
      if (!user) {
        throw new Error('Invalid email or password');
      }

      // Check if user is active
      if (!user.isActive) {
        throw new Error('Account is deactivated');
      }

      // Verify password
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        throw new Error('Invalid email or password');
      }

      // Update last login
      user.lastLogin = new Date();
      await user.save();

      // Generate JWT token
      const token = this.generateToken(user._id.toString());

      logger.info('User logged in successfully:', { id: user._id, email: user.email });
      return { user, token };
    } catch (error) {
      logger.error('Error logging in user:', { error, email });
      throw error;
    }
  }

  async getUserById(id: string): Promise<IUserDocument | null> {
    try {
      const user = await User.findById(id).exec();
      return user;
    } catch (error) {
      logger.error('Error getting user by ID:', { error, id });
      throw new Error('Failed to retrieve user');
    }
  }

  async getUserByEmail(email: string): Promise<IUserDocument | null> {
    try {
      const user = await User.findOne({ email }).exec();
      return user;
    } catch (error) {
      logger.error('Error getting user by email:', { error, email });
      throw new Error('Failed to retrieve user');
    }
  }

  async updateUser(id: string, updateData: Partial<IUser>): Promise<IUserDocument | null> {
    try {
      // Remove sensitive fields that shouldn't be updated directly
      const { password, ...safeUpdateData } = updateData;

      const updatedUser = await User.findByIdAndUpdate(
        id,
        { ...safeUpdateData, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).exec();

      if (updatedUser) {
        logger.info('User updated successfully:', { id, email: updatedUser.email });
      }

      return updatedUser;
    } catch (error) {
      logger.error('Error updating user:', { error, id, data: updateData });
      throw new Error('Failed to update user');
    }
  }

  async changePassword(id: string, currentPassword: string, newPassword: string): Promise<boolean> {
    try {
      const user = await User.findById(id).select('+password');
      if (!user) {
        throw new Error('User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await user.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        throw new Error('Current password is incorrect');
      }

      // Update password
      user.password = newPassword;
      await user.save();

      logger.info('Password changed successfully:', { id });
      return true;
    } catch (error) {
      logger.error('Error changing password:', { error, id });
      throw error;
    }
  }

  async deactivateUser(id: string): Promise<boolean> {
    try {
      const user = await User.findByIdAndUpdate(
        id,
        { isActive: false, updatedAt: new Date() },
        { new: true }
      ).exec();

      if (user) {
        logger.info('User deactivated successfully:', { id, email: user.email });
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Error deactivating user:', { error, id });
      throw new Error('Failed to deactivate user');
    }
  }

  async verifyToken(token: string): Promise<{ userId: string } | null> {
    try {
      const decoded = jwt.verify(token, config.jwt.secret) as { userId: string };
      return decoded;
    } catch (error) {
      logger.error('Error verifying token:', error);
      return null;
    }
  }
}
