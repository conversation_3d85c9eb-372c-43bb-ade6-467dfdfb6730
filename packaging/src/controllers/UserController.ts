import { Request, Response, NextFunction } from 'express';
import { UserService } from '../services/UserService';
import { ResponseUtil } from '../utils/response';
import { logger } from '../utils/logger';
import { AuthenticatedRequest } from '../middleware/auth';

export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  register = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userData = req.body;
      const result = await this.userService.register(userData);
      ResponseUtil.success(res, result, 'User registered successfully', 201);
    } catch (error) {
      logger.error('Error registering user', { error, data: req.body });
      next(error);
    }
  };

  login = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { email, password } = req.body;
      const result = await this.userService.login(email, password);
      ResponseUtil.success(res, result, 'Login successful');
    } catch (error) {
      logger.error('Error logging in user', { error, email: req.body.email });
      next(error);
    }
  };

  getProfile = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return ResponseUtil.error(res, 'User not authenticated', 401);
      }
      
      const user = await this.userService.getUserById(userId);
      ResponseUtil.success(res, user, 'Profile retrieved successfully');
    } catch (error) {
      logger.error('Error getting user profile', { error, userId: req.user?.id });
      next(error);
    }
  };

  updateProfile = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return ResponseUtil.error(res, 'User not authenticated', 401);
      }
      
      const updateData = req.body;
      const updatedUser = await this.userService.updateUser(userId, updateData);
      ResponseUtil.success(res, updatedUser, 'Profile updated successfully');
    } catch (error) {
      logger.error('Error updating user profile', { error, userId: req.user?.id, data: req.body });
      next(error);
    }
  };
}
