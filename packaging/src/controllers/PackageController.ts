import { Request, Response, NextFunction } from 'express';
import { PackageService } from '../services/PackageService';
import { ResponseUtil } from '../utils/response';
import { logger } from '../utils/logger';

export class PackageController {
  private packageService: PackageService;

  constructor() {
    this.packageService = new PackageService();
  }

  getAllPackages = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const packages = await this.packageService.getAllPackages();
      ResponseUtil.success(res, packages, 'Packages retrieved successfully');
    } catch (error) {
      logger.error('Error getting packages', { error });
      next(error);
    }
  };

  getPackageById = async (req: Request, res: Response, next: NextFunction): Promise<Response | void> => {
  ...
  if (!package_) {
    return ResponseUtil.error(res, 'Package not found', 404);
  }
  ...
}
getPackageById = async (req: Request, res: Response, next: NextFunction): Promise<Response | void> => {
    try {
      const { id } = req.params;
      const package_ = await this.packageService.getPackageById(id);

      if (!package_) {
        return ResponseUtil.error(res, 'Package not found', 404);
      }

      ResponseUtil.success(res, package_, 'Package retrieved successfully');
    } catch (error) {
      logger.error('Error getting package by ID', { error, id: req.params.id });
      next(error);
    }
  };

  createPackage = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const packageData = req.body;
      const newPackage = await this.packageService.createPackage(packageData);
      ResponseUtil.success(res, newPackage, 'Package created successfully', 201);
    } catch (error) {
      logger.error('Error creating package', { error, data: req.body });
      next(error);
    }
  };

  updatePackage = async (req: Request, res: Response, next: NextFunction): Promise<Response | void> => {
    try {
      const { id } = req.params;
      const updateData = req.body;
      const updatedPackage = await this.packageService.updatePackage(id, updateData);

      if (!updatedPackage) {
        return ResponseUtil.error(res, 'Package not found', 404);
      }

      ResponseUtil.success(res, updatedPackage, 'Package updated successfully');
    } catch (error) {
      logger.error('Error updating package', { error, id: req.params.id, data: req.body });
      next(error);
    }
  };

  deletePackage = async (req: Request, res: Response, next: NextFunction): Promise<Response | void> => {
    try {
      const { id } = req.params;
      const deleted = await this.packageService.deletePackage(id);

      if (!deleted) {
        return ResponseUtil.error(res, 'Package not found', 404);
      }

      ResponseUtil.success(res, null, 'Package deleted successfully');
    } catch (error) {
      logger.error('Error deleting package', { error, id: req.params.id });
      next(error);
    }
  };
}
